# 26 加餐：项目中 2 个真实的类型编程案例
最近有两个同学问了我项目中遇到的 ts 问题，这俩问题都是典型的可以用类型编程来解决的。

这俩都是项目中真实遇到的 TS 类型问题，我们一起看一下吧：

第一个问题是这样的，项目中定义了接口返回的数据的类型，比如这样：

![image](images/5ZERn6qobmPGGsbq7XEU2tLC5ZiPjEUx6fCveRXP2js.webp)

那么填充数据的时候就要根据类型的定义来写：

![image](images/OFworDtAjpcArYC7PKnhdMYoNi4sj2ozgUF2S1pqlGE.webp)

但是呢，如果你想扩展一些属性就报错了：

![image](images/uAn9VCCwB_txTiGecAjvSM-CgWg4Qv011KtH0XGyjZs.webp)

但现在想每层都能灵活扩展一些属性，怎么做呢？

简化一下就是这样的：

![image](images/ama7018ojSSYYctfieL3Es0jqr7s_GIkJ0AzUR3Iazs.webp)

如何能让这个索引类型可以灵活添加一些额外的索引呢？

可以这样，添加一个可索引签名

![image](images/XALtooIEYA56n36yR1muRVeBrPb8KTnIgJYMw4-MQyE.webp)

能满足这个索引签名的额外索引都可以添加。

也可以这样写：

![image](images/dtMqTHa7dpSGIASZPxiMoRznzrbcqpLfGjHK9Ut15kU.webp)

和 Record<string, any> 取交叉类型。

这个 Record 是一个内置的高级类型，作用是根据传入的 key 和 value 的类型生成索引类型：

![image](images/Dk2gq4v9f-9R64EMiG1qbLm2nb1Ly_QxwVMLgnxBxL0.webp)

这种生成索引类型的语法叫做映射类型。

所以，Record<string, any> 就是这样的，也是一个有可索引签名的索引类型：

![image](images/3CH3r7_tBIJJId01AWYUQmS2bYiYAajlsv5Dr7QiIcE.webp)

普通的对象我们知道怎么处理了，那多层的呢？

这样任意层数的索引类型，怎么给每一层都加上 Record<string, any> 呢？

![image](images/CGMjDOGIH9lKNC8USN5XRn8JagSu8shHgavIvvO07no.webp)

这时候就要用到递归了，可以这样写：

```Plain Text
type DeepRecord<Obj extends Record<string, any>> = {
    [Key in keyof Obj]: 
        Obj[Key] extends Record<string, any>
            ? DeepRecord<Obj[Key]> & Record<string, any>
            : Obj[Key]
} & Record<string, any>;

```
定义一个 DeepRecord 的高级类型，传入的类型参数 Obj 为一个索引类型，通过 Record<string, any> 约束。

然后通过映射类型的语法构造一个新的索引类型。

Key 来自之前的索引类型的 Key，也就是 Key in keyof Obj。

Value 要判断是不是索引类型，如果依然是 Record<string, any>，那就递归处理它的值 Obj\[Key\]，否则直接返回 Obj\[Key\]。

每一层都要和 Record<string, any> 取交叉类型。

这样就完成了递归让 Obj 的每一层都变得可扩展的目的。

我们测试一下：

![image](images/WPLOGKGrfDhDktdXUOQqlASTFBpDd2rrIL_U365W-A0.webp)

可以看到，处理过后的类型确实是每一层都加上了 Record<string, any>。

也确实每一层都可以扩展了：

![image](images/IVOHpdBzyxkQTRXe0NjYIBWfnrwG9vbnjjNZw72PaSc.webp)

并且有类型定义的索引也会做类型检查：

![image](images/TmhBZQr3-VOvW3vYMz6-Uz-AGYrw5GpBiqZXzY4imU8.webp)

小结一下：**可索引签名可以让索引类型扩展任意数量的符合签名的索引，如果想给任意层级的索引每层都加上可索引签名就要递归处理了。**

那如果不用类型编程呢？

那你就要原封不动的写一个新的索引类型，然后手动给每一层都加上可索引签名，那就麻烦太多了，而且也不通用。

这就是类型编程的意义之一，可以根据需要修改类型。

[案例一的 ts playground 地址](https://link.juejin.cn/?target=https%3A%2F%2Fwww.typescriptlang.org%2Fplay%3F%23code%2FC4TwDgpgBAIghsOUC8UDeAoK2pzwLigDsBXAWwCMIAnAbixwqcMxzagGMvDTKb72OACYjCAZ2DUAlkQDmAnAF8ANA2wQNLNewBme8ZJnztbEUJ7kqdbYoy3QkWBrAAlCBwD21IQB4A8hQAVlAQAB7AEERCYlBunt4%2BEtJyyrhEIAB8GSjo2gDaANIQIFAyUADWxR46UAGBALqEJjh1hcX1IeGR0bHuXr5JRqlw6RnNggD8ThCufQmtRSD12QBkvfEDhilpmePshAvtdlBrcf2JW7LDo-QYDtDUEDGoMM5nCfCIGbeeRBJQQgQcEInyQqFYODwwKgAEZVGwmBQtIJsFwOIQ4XszIQAORQnE2eE4DQQZEovQ6XGInFEwTYqAAJhsdiAA "https://www.typescriptlang.org/play?#code/C4TwDgpgBAIghsOUC8UDeAoK2pzwLigDsBXAWwCMIAnAbixwqcMxzagGMvDTKb72OACYjCAZ2DUAlkQDmAnAF8ANA2wQNLNewBme8ZJnztbEUJ7kqdbYoy3QkWBrAAlCBwD21IQB4A8hQAVlAQAB7AEERCYlBunt4+EtJyyrhEIAB8GSjo2gDaANIQIFAyUADWxR46UAGBALqEJjh1hcX1IeGR0bHuXr5JRqlw6RnNggD8ThCufQmtRSD12QBkvfEDhilpmePshAvtdlBrcf2JW7LDo-QYDtDUEDGoMM5nCfCIGbeeRBJQQgQcEInyQqFYODwwKgAEZVGwmBQtIJsFwOIQ4XszIQAORQnE2eE4DQQZEovQ6XGInFEwTYqAAJhsdiAA")

再来看第二个问题：

![image](images/fOnEWDaMTzINAFxIDoDTLVikocfCtNGwdgzuuNvhxlU.webp)

也就是当一个索引为 'desc' | 'asc' 的时候，其他索引都是 false。

这种类型怎么写呢？

有的同学说，这个就是枚举所有的情况呀，比如这样：

![image](images/M1V7uFawsFGx5W6zpNGO3p9ueZe7QtCBsnCXtubQ1FE.webp)

这确实能解决问题：

![image](images/hASwT6Ywri3qfju2uqLlq8fQtEa5KO6meyDKJsDSyyY.webp)

可以看到类型检查是符合我们的需求的。

但如果我再加几个属性呢？

是不是可能的类型又多了几种？

手动维护也太麻烦了！

这时候就可以用类型编程动态生成了。

比如我定义这样一个高级类型：

```Plain Text
type GenerateType<Keys extends string> = {
    [Key in Keys]: {
        [Key2 in Key]: 'desc' | 'asc'
    }
}

```
它生成的类型是这样的：

![image](images/8LcgUEfboZKKyr73b_CdwByV6zbkntJIbFSMRgRrB_c.webp)

这个还是很容易理解的，映射类型就是用来生成索引类型的。

我们可以取它的值：

![image](images/2snmnyzs_Os2TmE-vOFpcOJrt1etb2Mb7CVPYTbvqTA.webp)

结果就是这样的：

![image](images/79sJhiv_G3SDzWAMzyAONBSJSzlroarZsU2YhyzkqAE.webp)

现在就只差那些为 false 的索引了。

Keys 是一个联合类型，从中去掉 Key 的类型，可以用 Exclude，也就是 Exclude<Keys, Key>。

那么这个类型就可以这么写：

```Plain Text
type GenerateType<Keys extends string> = {
    [Key in Keys]: {
        [Key2 in Key]: 'desc' | 'asc'
    } & {
        [Key3 in Exclude<Keys, Key>]: false
    }
}[Keys]

```
结果就是我们要的类型：

![image](images/6cwYrMEe6WTwwycETXoYf3zfl626ckn2_nYJcwy-Grw.webp)

任意多个索引都可以动态生成复合需求的联合类型。

![image](images/vg4p3koEaCI5kB4RyyxpT8NPZHs5iX0FqM61vS2NRiE.webp)

上面这个高级类型还可以做一些优化，把 key 的约束换成 keyof any：

![image](images/veUp9JTZroV3hihzSpCwGHJkYocLYa0vFNiaL8gXihQ.webp)

keyof any 的结果就是索引的类型：

![image](images/LBPwl0FQvZuI9peOlRANbfE1MscxxQ3-bxcEIrl0uLw.webp)

但有个配置项叫做 keyofStringsOnly

![image](images/eeBupetVBzLTWZ1Y4DIQ4W2eyTRj3SzfD2BdMLKflhs.webp)

开启之后就只能是 string 作为 key 了：

![image](images/e80I7hOlF-OB95x52xMGquVGxwfnMlAi_lZyJFFDBHg.webp)

keyof any 就可以动态获取 key 的可能类型，比写死更好。

这个高级类型最终就是这样的：

```Plain Text
type GenerateType<Keys extends keyof any> = {
    [Key in Keys]: {
        [Key2 in Key]: 'desc' | 'asc'
    } & {
        [Key3 in Exclude<Keys, Key>]: false
    }
}[Keys]

```
小结一下：**当需要枚举很多种类型的可能性的时候，可以用类型编程动态生成。**

那如果不用类型编程呢？

那你就得手动维护所有的可能类型了。

这就是类型编程的第二个意义，可以动态生成类型。

[案例二的 ts playground 地址](https://link.juejin.cn/?target=https%3A%2F%2Fwww.typescriptlang.org%2Fplay%3F%23code%2FC4TwDgpgBA4hB2EBOBDYEAq4IB4DSEIAzlBAB7rwAmJA1oQPYBmUK8IAfFALxQDeAKCjCoAbQIgoAS3hQJRALoAufkJHrxhAEzTZE5VADkVCEQDGhqAB8jKc4bXCAvlABkq9RokBmXVACiZGYANgCuJviERAA0coQcBkwowUQQjlBOAk6axAoA3AICoJBQSKY8sAjIaJjYOIYojZY2hgBG7c1GZt2GHAUCZgzwRMCsSmUkvILqjSgqDfbR6e2tKkkpEEvq3WZryalZhYPDo6ul5VPps3sbWyIr8yaL6Ts3B5kDQyNQu%2BeTHiJrrZnuoHkYnhY7sJXlB1u8BEA "https://www.typescriptlang.org/play?#code/C4TwDgpgBA4hB2EBOBDYEAq4IB4DSEIAzlBAB7rwAmJA1oQPYBmUK8IAfFALxQDeAKCjCoAbQIgoAS3hQJRALoAufkJHrxhAEzTZE5VADkVCEQDGhqAB8jKc4bXCAvlABkq9RokBmXVACiZGYANgCuJviERAA0coQcBkwowUQQjlBOAk6axAoA3AICoJBQSKY8sAjIaJjYOIYojZY2hgBG7c1GZt2GHAUCZgzwRMCsSmUkvILqjSgqDfbR6e2tKkkpEEvq3WZryalZhYPDo6ul5VPps3sbWyIr8yaL6Ts3B5kDQyNQu+eTHiJrrZnuoHkYnhY7sJXlB1u8BEA")

通过这两个真实的案例，不知道你是否体会到类型编程解决了什么问题呢？

**当你需要修改已有的类型，或者动态生成类型，都可以用类型编程。**

第一个案例，我们递归给每一层加上了可索引签名，不需要手动一层层改。

第二个案例，我们动态生成了所有的可能类型，不需要手动枚举。

类型编程的意义，你感受到了么？
# 7 套路三：递归复用做循环
会做类型的提取和构造之后，我们已经能写出很多类型编程逻辑了，但是有时候提取或构造的数组元素个数不确定、字符串长度不确定、对象层数不确定。这时候怎么办呢？

其实前面的案例我们已经涉及到了一些，就是递归。

这就是第三个类型体操套路：递归复用做循环。

## 递归复用
**递归是把问题分解为一系列相似的小问题，通过函数不断调用自身来解决这一个个小问题，直到满足结束条件，就完成了问题的求解。**

TypeScript 的高级类型支持类型参数，可以做各种类型运算逻辑，返回新的类型，和函数调用是对应的，自然也支持递归。

**TypeScript 类型系统不支持循环，但支持递归。当处理数量（个数、长度、层数）不固定的类型的时候，可以只处理一个类型，然后递归的调用自身处理下一个类型，直到结束条件也就是所有的类型都处理完了，就完成了不确定数量的类型编程，达到循环的效果。**

既然提到了数组、字符串、对象等类型，那么我们就来看一下这些类型的递归案例吧。

## Promise 的递归复用
### DeepPromiseValueType
先用 Promise 热热身，实现一个提取不确定层数的 Promise 中的 value 类型的高级类型。

```Plain Text
type ttt = Promise<Promise<Promise<Record<string, any>>>>;

```
这里是 3 层 Promise，value 类型是索引类型。

数量不确定，一涉及到这个就要想到用递归来做，每次只处理一层的提取，然后剩下的到下次递归做，直到结束条件。

所以高级类型是这样的：

```Plain Text
type DeepPromiseValueType<P extends Promise<unknown>> =
    P extends Promise<infer ValueType> 
        ? ValueType extends Promise<unknown>
            ? DeepPromiseValueType<ValueType>
            : ValueType
        : never;

```
类型参数 P 是待处理的 Promise，通过 extends 约束为 Promise 类型，value 类型不确定，设为 unknown。

每次只处理一个类型的提取，也就是通过模式匹配提取出 value 的类型到 infer 声明的局部变量 ValueType 中。

然后判断如果 ValueType 依然是 Promise类型，就递归处理。

结束条件就是 ValueType 不为 Promise 类型，那就处理完了所有的层数，返回这时的 ValueType。

这样，我们就提取到了最里层的 Promise 的 value 类型，也就是索引类型：

![image](images/x4m2QdazWvCQTydsxHBlkVC9ieb2jPB0R_oSHAUNaL8.webp)

[试一下](https://link.juejin.cn/?target=https%3A%2F%2Fwww.typescriptlang.org%2Fplay%3F%23code%2FC4TwDgpgBAIhFgAoCcD2BbAlgZwgNQEMAbAVwgBVwIAeRKCAD2AgDsATbKFDHGklgNYtUAdxYA%2BcVAC8AKCgKu9Jqw5c0WXNUwsAZhGRRCpClSnzFlgPxHiZSpGXN2nbpr6DhY8Rct%2BoNnAIbrzG9lTUYaaQPv7%2BAFy2Jg4Qvn6JLBAAbgYA3LKyoI5BSBq8AEoQ2CREwDKw8KU8uFEptGVaIVqVAMaoyGzU2MDIOgDmADRQBCwgkpK5QA "https://www.typescriptlang.org/play?#code/C4TwDgpgBAIhFgAoCcD2BbAlgZwgNQEMAbAVwgBVwIAeRKCAD2AgDsATbKFDHGklgNYtUAdxYA+cVAC8AKCgKu9Jqw5c0WXNUwsAZhGRRCpClSnzFlgPxHiZSpGXN2nbpr6DhY8Rct+oNnAIbrzG9lTUYaaQPv7+AFy2Jg4Qvn6JLBAAbgYA3LKyoI5BSBq8AEoQ2CREwDKw8KU8uFEptGVaIVqVAMaoyGzU2MDIOgDmADRQBCwgkpK5QA")

其实这个类型的实现可以进一步的简化：

```Plain Text
type DeepPromiseValueType2<T> = 
    T extends Promise<infer ValueType> 
        ? DeepPromiseValueType2<ValueType>
        : T;

```
不再约束类型参数必须是 Promise，这样就可以少一层判断。

![image](images/-mjNpQQjqWeIwumeuluzSJBO_CjEr2wYA7-45dA3Kd8.webp)

[试一下](https://link.juejin.cn/?target=https%3A%2F%2Fwww.typescriptlang.org%2Fplay%3F%23code%2FC4TwDgpgBAIhFgAoCcD2BbAlgZwgNQEMAbAVwgBVwIAeRKCAD2AgDsATbKFDHGklgNYtUAdxYA%2BcVAC8AKCgKu9Jqw5c0WXNUwsAZhGRRCpClSnzFlgPxHiZSpGXN2nbpr6DhY8Rct%2BoNnAIbrzG9lTUYaaQPv7%2BAFy2Jg4Qvn6JLBAAbgYA3LKyoI5BSBq8AEoQ2CREwDKw8KU8uFEptGVaIVqVAMaoyGzU2MDIOgDmADRQBCwgkpL5hVQNwR34dtEQAEzU5FLSUGnkTqqua9p6BknhMYdxAStN7q1UOy8xaZaJ5ItF0CVddbJV6VTgHAFrd7bdrNGiAmHuagsEjoABGBnm4nyQA "https://www.typescriptlang.org/play?#code/C4TwDgpgBAIhFgAoCcD2BbAlgZwgNQEMAbAVwgBVwIAeRKCAD2AgDsATbKFDHGklgNYtUAdxYA+cVAC8AKCgKu9Jqw5c0WXNUwsAZhGRRCpClSnzFlgPxHiZSpGXN2nbpr6DhY8Rct+oNnAIbrzG9lTUYaaQPv7+AFy2Jg4Qvn6JLBAAbgYA3LKyoI5BSBq8AEoQ2CREwDKw8KU8uFEptGVaIVqVAMaoyGzU2MDIOgDmADRQBCwgkpL5hVQNwR34dtEQAEzU5FLSUGnkTqqua9p6BknhMYdxAStN7q1UOy8xaZaJ5ItF0CVddbJV6VTgHAFrd7bdrNGiAmHuagsEjoABGBnm4nyQA")

接下来再看下数组类型的递归复用：

## 数组类型的递归
### ReverseArr
有这样一个元组类型：

```Plain Text
type arr = [1,2,3,4,5];

```
我们把它反过来，也就是变成：

```Plain Text
type arr = [5,4,3,2,1];

```
这个学完了提取和构造很容易写出来：

```Plain Text
type ReverseArr<Arr extends unknown[]> = 
    Arr extends [infer One, infer Two, infer Three, infer Four, infer Five]
        ? [Five, Four, Three, Two, One]
        : never;

```
![image](images/CVSBOF1k4K5rqlpg6p5hfqF3RfQ8zwudBcTYRrdF5C4.webp)

但如果数组长度不确定呢？

数量不确定，条件反射的就要想到递归。

我们每次只处理一个类型，剩下的递归做，直到满足结束条件。

```Plain Text
type ReverseArr<Arr extends unknown[]> = 
    Arr extends [infer First, ...infer Rest] 
        ? [...ReverseArr<Rest>, First] 
        : Arr;

```
类型参数 Arr 为待处理的数组类型，元素类型不确定，也就是 unknown。

每次只处理一个元素的提取，放到 infer 声明的局部变量 First 里，剩下的放到 Rest 里。

用 First 作为最后一个元素构造新数组，其余元素递归的取。

结束条件就是取完所有的元素，也就是不再满足模式匹配的条件，这时候就返回 Arr。

![image](images/mkCFzq7T6zNj5ttn7Ma6ZxDl6xV_PmIQ7D-r59TAoCY.webp)

[试一下](https://link.juejin.cn/?target=https%3A%2F%2Fwww.typescriptlang.org%2Fplay%3F%23code%2FC4TwDgpgBAShBuEBOBnCBBJSA8mlQgA9gIA7AExSgFdSBrUgewHdSBtAXQD4oBeKAFBRhUPAWJlKUNgEtSAM2RQAYjNTAANFAB0uuYvxwUwDoJHmoAfmm7tcRKgxZsR4Fy2r1poReEAuUSwAbgEBUEhYBGQ0PCNqABtgPkiHGOc2AEYNACYNAGYNABYNAFZuIKA "https://www.typescriptlang.org/play?#code/C4TwDgpgBAShBuEBOBnCBBJSA8mlQgA9gIA7AExSgFdSBrUgewHdSBtAXQD4oBeKAFBRhUPAWJlKUNgEtSAM2RQAYjNTAANFAB0uuYvxwUwDoJHmoAfmm7tcRKgxZsR4Fy2r1poReEAuUSwAbgEBUEhYBGQ0PCNqABtgPkiHGOc2AEYNACYNAGYNABYNAFZuIKA")

### Includes
既然递归可以做循环用，那么像查找元素这种自然也就可以实现。

比如查找 \[1, 2, 3, 4, 5\] 中是否存在 4，是就返回 true，否则返回 false。

从长度不固定的数组中查找某个元素，数量不确定，这时候就应该想到递归。

```Plain Text
type Includes<Arr extends unknown[], FindItem> = 
    Arr extends [infer First, ...infer Rest]
        ? IsEqual<First, FindItem> extends true
            ? true
            : Includes<Rest, FindItem>
        : false;

type IsEqual<A, B> = (A extends B ? true : false) & (B extends A ? true : false);

```
类型参数 Arr 是待查找的数组类型，元素类型任意，也就是 unknown。FindItem 待查找的元素类型。

每次提取一个元素到 infer 声明的局部变量 First 中，剩余的放到局部变量 Rest。

判断 First 是否是要查找的元素，也就是和 FindItem 相等，是的话就返回 true，否则继续递归判断下一个元素。

直到结束条件也就是提取不出下一个元素，这时返回 false。

相等的判断就是 A 是 B 的子类型并且 B 也是 A 的子类型，。

这样就完成了不确定长度的数组中的元素查找，用递归实现了循环。

当包含时：

![image](images/T-opXrmvJeJNt-gNXlaIRDxp0a0J_Io_Qye209FQ-dc.webp)

当不包含时：

![image](images/XelZjCBuLGvvdVBG-_UTCDt6pkfVdk5Bpv-pIlZ06Do.webp)

[试一下](https://link.juejin.cn/?target=https%3A%2F%2Fwww.typescriptlang.org%2Fplay%3F%23code%2FFAFwngDgpgBAkgOwMYBsCuATKBnAPAQQCdCYoAPEKBDbGNBAawQHsB3BAbQF0AaGAMQCW1OJQC2APhgBeGMBgKYREuUrVaHYQDMoJIYWwg%2BAOlPbdMAEo4QXeYocB%2BeNgCiARzQBDFLn2G%2BIRFxKVUqGhgQQjQoewd4mGcomLiExQAueGR0LDxrAIFhDFEoSVSHTK0fbCgAbmBQSFg4N08fAj4AISlZAAp8Ugpw2k7EyOjYSuqoAEoYADIYXtGw9SUx5MmYKpQamfrG6CzUTBx8tBQQGWOcnFwOAEY%2BACY%2BAGY%2BABY%2BAFZeGE%2BEgO4COiBOuXOl2e1zBtzwjxe7y%2Bv3%2BADYgUA "https://www.typescriptlang.org/play?#code/FAFwngDgpgBAkgOwMYBsCuATKBnAPAQQCdCYoAPEKBDbGNBAawQHsB3BAbQF0AaGAMQCW1OJQC2APhgBeGMBgKYREuUrVaHYQDMoJIYWwg+AOlPbdMAEo4QXeYocB+eNgCiARzQBDFLn2G+IRFxKVUqGhgQQjQoewd4mGcomLiExQAueGR0LDxrAIFhDFEoSVSHTK0fbCgAbmBQSFg4N08fAj4AISlZAAp8Ugpw2k7EyOjYSuqoAEoYADIYXtGw9SUx5MmYKpQamfrG6CzUTBx8tBQQGWOcnFwOAEY+ACY+AGY+ABY+AFZeGE+EgO4COiBOuXOl2e1zBtzwjxe7y+v3+ADYgUA")

### RemoveItem
可以查找自然就可以删除，只需要改下返回结果，构造一个新的数组返回。

```Plain Text
type RemoveItem<
    Arr extends unknown[], 
    Item, 
    Result extends unknown[] = []
> = Arr extends [infer First, ...infer Rest]
        ? IsEqual<First, Item> extends true
            ? RemoveItem<Rest, Item, Result>
            : RemoveItem<Rest, Item, [...Result, First]>
        : Result;

type IsEqual<A, B> = (A extends B ? true : false) & (B extends A ? true : false);

```
类型参数 Arr 是待处理的数组，元素类型任意，也就是 unknown\[\]。类型参数 Item 为待查找的元素类型。类型参数 Result 是构造出的新数组，默认值是 \[\]。

通过模式匹配提取数组中的一个元素的类型，如果是 Item 类型的话就删除，也就是不放入构造的新数组，直接返回之前的 Result。

否则放入构造的新数组，也就是再构造一个新的数组 \[...Result, First\]。

直到模式匹配不再满足，也就是处理完了所有的元素，返回这时候的 Result。

这样我们就完成了不确定元素个数的数组的某个元素的删除：

![image](images/dAare8NKb7Bk-Nh4F0LOWv5Ipm_ZF5fvBMfFf9f2fxY.webp)

[试一下](https://link.juejin.cn/?target=https%3A%2F%2Fwww.typescriptlang.org%2Fplay%3F%23code%2FC4TwDgpgBAShC2B7AbhAksBAeAggJzyggA9MA7AEwGcoBXMgazMQHcyBtAXQBooMFecKrQA2wIqQiUa9Jqw6coAXihcAfMqgAoKLqj5CJctVUBLMgDMIhAGKm8VYLwB0r81cJDgnHXr8B%2BPioAUQBHWgBDESw7Byc%2BTHgNIykTYDxaCF8-HKhAuCRUfngsL15iwQhhMTVs3L0ALlgEFHRE0qr4itVXZyFReNjHTlr6qCb%2BsQBuLS1QSCCwyOicXgAhDRUAChwJYxo1vKh0zPGoCyiqCABKKAAyKC3DlOl9I5PoJouRK%2BuZufA0AKrWKk3EKmBRXa7AAjNwAEwI7gAZh4UHhaimQA "https://www.typescriptlang.org/play?#code/C4TwDgpgBAShC2B7AbhAksBAeAggJzyggA9MA7AEwGcoBXMgazMQHcyBtAXQBooMFecKrQA2wIqQiUa9Jqw6coAXihcAfMqgAoKLqj5CJctVUBLMgDMIhAGKm8VYLwB0r81cJDgnHXr8B+PioAUQBHWgBDESw7Byc+THgNIykTYDxaCF8-HKhAuCRUfngsL15iwQhhMTVs3L0ALlgEFHRE0qr4itVXZyFReNjHTlr6qCb+sQBuLS1QSCCwyOicXgAhDRUAChwJYxo1vKh0zPGoCyiqCABKKAAyKC3DlOl9I5PoJouRK+uZufA0AKrWKk3EKmBRXa7AAjNwAEwI7gAZh4UHhaimQA")

### BuildArray
我们学过数组类型的构造，如果构造的数组类型元素个数不确定，也需要递归。

比如传入 5 和元素类型，构造一个长度为 5 的该元素类型构成的数组。

```Plain Text
type BuildArray<
    Length extends number, 
    Ele = unknown, 
    Arr extends unknown[] = []
> = Arr['length'] extends Length 
        ? Arr 
        : BuildArray<Length, Ele, [...Arr, Ele]>;

```
类型参数 Length 为数组长度，约束为 number。类型参数 Ele 为元素类型，默认值为 unknown。类型参数 Arr 为构造出的数组，默认值是 \[\]。

每次判断下 Arr 的长度是否到了 Length，是的话就返回 Arr，否则在 Arr 上加一个元素，然后递归构造。

![image](images/1FM_tsEP5BpVV2PEEWAE-tmksnZ15tqZPId7khevd70.webp)

[试一下](https://link.juejin.cn/?target=https%3A%2F%2Fwww.typescriptlang.org%2Fplay%3F%23code%2FFAFwngDgpgBAQgVwJYBsAmBBATlghmAHgBkoA7AcxAAsYoAPEMtAZxlIQFsAjKLAGhjAYwmAFEUsALwwEpANakA9gHdSAoSOxZaDJq1kKVpANoBdGNLMA%2BCxuFbjAcgkVqj8-UakWMEq5p2IsIA-DBagkFBAFzwyOha%2BMRklFQC4lACxgB0OVppEqZWANzAoJCwiKiYOABKUMwIKCAWsVUJhACsxUA "https://www.typescriptlang.org/play?#code/FAFwngDgpgBAQgVwJYBsAmBBATlghmAHgBkoA7AcxAAsYoAPEMtAZxlIQFsAjKLAGhjAYwmAFEUsALwwEpANakA9gHdSAoSOxZaDJq1kKVpANoBdGNLMA+CxuFbjAcgkVqj8-UakWMEq5p2IsIA-DBagkFBAFzwyOha+MRklFQC4lACxgB0OVppEqZWANzAoJCwiKiYOABKUMwIKCAWsVUJhACsxUA")

学完了数组类型的递归，我们再来看下字符串类型。

## 字符串类型的递归
### ReplaceAll
学模式匹配的时候，我们实现过一个 Replace 的高级类型：

```Plain Text
type ReplaceStr<
    Str extends string,
    From extends string,
    To extends string
> = Str extends `${infer Prefix}${From}${infer Suffix}` 
    ? `${Prefix}${To}${Suffix}` : Str;

```
它能把一个字符串中的某个字符替换成另一个：

![image](images/Rx9Vbn6S8Ytd5mr6MTjvOzJkzJNwoedhRGGS-Xxx-tc.webp)

但是如果有多个这样的字符就处理不了了。

如果不确定有多少个 From 字符，怎么处理呢？

**在类型体操里，遇到数量不确定的问题，就要条件反射的想到递归。**

每次递归只处理一个类型，这部分我们已经实现了，那么加上递归的调用就可以。

```Plain Text
type ReplaceAll<
    Str extends string, 
    From extends string, 
    To extends string
> = Str extends `${infer Left}${From}${infer Right}`
        ? `${Left}${To}${ReplaceAll<Right, From, To>}`
        : Str;

```
类型参数 Str 是待处理的字符串类型，From 是待替换的字符，To 是替换到的字符。

通过模式匹配提取 From 左右的字符串到 infer 声明的局部变量 Left 和 Right 里。

用 Left 和 To 构造新的字符串，剩余的 Right 部分继续递归的替换。

结束条件是不再满足模式匹配，也就是没有要替换的元素，这时就直接返回字符串 Str。

这样就实现了任意数量的字符串替换：

![image](images/krbpuVzsQO4j4PXu-rjJ1HVwarC76tQyRmaXDkrNRAo.webp)

[试一下](https://link.juejin.cn/?target=https%3A%2F%2Fwww.typescriptlang.org%2Fplay%3F%23code%2FFDAuE8AcFMAICVqQDYEMDG0CCzkB4BlUAJ1mgA9RoA7AEwGdZ6SBLagcwBpZhY-YAYsQD2AWzKUaDJqw7de-ACrCJVOo2bE27AHywAvD37HYRUhTXSABgBIA3mwBm0UgBloj0AF97QsT4dqZ1J4FnYAC28rBRMTAH5YWzt3TwDlAMQUDGxcPFCI0G4-UW5lHS9o2NiALlMSAG4QCBgEJDRMHGREegBXZFADVqyO3IBydh7UDlgJqfYZyY5R7nHF9mXYUdphJZ16oA "https://www.typescriptlang.org/play?#code/FDAuE8AcFMAICVqQDYEMDG0CCzkB4BlUAJ1mgA9RoA7AEwGdZ6SBLagcwBpZhY-YAYsQD2AWzKUaDJqw7de-ACrCJVOo2bE27AHywAvD37HYRUhTXSABgBIA3mwBm0UgBloj0AF97QsT4dqZ1J4FnYAC28rBRMTAH5YWzt3TwDlAMQUDGxcPFCI0G4-UW5lHS9o2NiALlMSAG4QCBgEJDRMHGREegBXZFADVqyO3IBydh7UDlgJqfYZyY5R7nHF9mXYUdphJZ16oA")

### StringToUnion
我们想把字符串字面量类型的每个字符都提取出来组成联合类型，也就是把 'dong' 转为 'd' | 'o' | 'n' | 'g'。

怎么做呢？

很明显也是提取和构造：

```Plain Text
type StringToUnion<Str extends string> = 
    Str extends `${infer One}${infer Two}${infer Three}${infer Four}`
        ? One | Two | Three | Four
        : never;

```
![image](images/q96MFsWpcwj5rGtmI0hNr2IAnlD_W-_YIIM2jbQb-c4.webp)

但如果字符串长度不确定呢？

数量不确定，在类型体操中就要条件反射的想到递归。

```Plain Text
type StringToUnion<Str extends string> = 
    Str extends `${infer First}${infer Rest}`
        ? First | StringToUnion<Rest>
        : never;

```
类型参数 Str 为待处理的字符串类型，通过 extends 约束为 string。

通过模式匹配提取第一个字符到 infer 声明的局部变量 First，其余的字符放到局部变量 Rest。

用 First 构造联合类型，剩余的元素递归的取。

这样就完成了不确定长度的字符串的提取和联合类型的构造：

![image](images/P8iczMGNorEhz4yYnAlu1Q9KdQMSgZsgiXvJoMk05QE.webp)

[试一下](https://link.juejin.cn/?target=https%3A%2F%2Fwww.typescriptlang.org%2Fplay%3F%23code%2FC4TwDgpgBAysBOBLAdgcwCoHsCqzGeQB454oIAPYCZAEwGco6EVUA%2BKAXigCgo-YEZStXpQABgBIA3igBmEUgDFE8JgF9pchVABKEdWN79jAfijLVwKAB8BSNFlz4iepqyPG%2BALijIIANwUAbm5Q0Eg7Fkc8AlcAVwAbKy4SKJwYogByAAsIBITMTNYQoA "https://www.typescriptlang.org/play?#code/C4TwDgpgBAysBOBLAdgcwCoHsCqzGeQB454oIAPYCZAEwGco6EVUA+KAXigCgo-YEZStXpQABgBIA3igBmEUgDFE8JgF9pchVABKEdWN79jAfijLVwKAB8BSNFlz4iepqyPG+ALijIIANwUAbm5Q0Eg7Fkc8AlcAVwAbKy4SKJwYogByAAsIBITMTNYQoA")

### ReverseStr
我们实现了数组的反转，自然也可以实现字符串类型的反转。

同样是递归提取和构造。

```Plain Text
type ReverseStr<
    Str extends string, 
    Result extends string = ''
> = Str extends `${infer First}${infer Rest}` 
    ? ReverseStr<Rest, `${First}${Result}`> 
    : Result;

```
类型参数 Str 为待处理的字符串。类型参数 Result 为构造出的字符，默认值是空串。

通过模式匹配提取第一个字符到 infer 声明的局部变量 First，其余字符放到 Rest。

用 First 和之前的 Result 构造成新的字符串，把 First 放到前面，因为递归是从左到右处理，那么不断往前插就是把右边的放到了左边，完成了反转的效果。

直到模式匹配不满足，就处理完了所有的字符。

这样就完成了字符串的反转：

![image](images/3WWAumRPRsKkRaBsoc70xOzHaln19w24hMsgO_c7qzQ.webp)

[试一下](https://link.juejin.cn/?target=https%3A%2F%2Fwww.typescriptlang.org%2Fplay%3F%23code%2FFAFwngDgpgBASlAblATgZygZRCgPNlGKADxCgDsATNGNHAS3IHMAaGYGT%2BKNAVwBsQRUhWq0GzGAF4YAclkA%2Baey4wCwslRoADACQBvRgDNUMAGL10IAL4HjphHWvaVqrgH5uydFhy5HIGx6%2BhZWtvqOAjbaShxunABc3HyCANzAoJCwCN4YBJGCyjmoeX6yABZQ-PwA9oqpQA "https://www.typescriptlang.org/play?#code/FAFwngDgpgBASlAblATgZygZRCgPNlGKADxCgDsATNGNHAS3IHMAaGYGT+KNAVwBsQRUhWq0GzGAF4YAclkA+aey4wCwslRoADACQBvRgDNUMAGL10IAL4HjphHWvaVqrgH5uydFhy5HIGx6+hZWtvqOAjbaShxunABc3HyCANzAoJCwCN4YBJGCyjmoeX6yABZQ-PwA9oqpQA")

学完了字符串的递归，我们再来看下对象的。

## 对象类型的递归
### DeepReadonly
对象类型的递归，也可以叫做索引类型的递归。

我们之前实现了索引类型的映射，给索引加上了 readonly 的修饰：

```Plain Text
type ToReadonly<T> =  {
    readonly [Key in keyof T]: T[Key];
}

```
![image](images/7txffsIr_mOj5dxiQ7lFQuL5Xf8ELHWNh2sMqWGWWzc.webp)

如果这个索引类型层数不确定呢？

比如这样：

```Plain Text
type obj = {
    a: {
        b: {
            c: {
                f: () => 'dong',
                d: {
                    e: {
                        guang: string
                    }
                }
            }
        }
    }
}

```
数量（层数）不确定，类型体操中应该自然的想到递归。

我们在之前的映射上加入递归的逻辑：

```Plain Text
type DeepReadonly<Obj extends Record<string, any>> = {
    readonly [Key in keyof Obj]:
        Obj[Key] extends object
            ? Obj[Key] extends Function
                ? Obj[Key] 
                : DeepReadonly<Obj[Key]>
            : Obj[Key]
}

```
类型参数 Obj 是待处理的索引类型，约束为 Record<string, any>，也就是索引为 string，值为任意类型的索引类型。

索引映射自之前的索引，也就是 Key in keyof Obj，只不过加上了 readonly 的修饰。

值要做下判断，如果是 object 类型并且还是 Function，那么就直接取之前的值 Obj\[Key\]。

如果是 object 类型但不是 Function，那就是说也是一个索引类型，就递归处理 DeepReadonly<Obj\[Key\]>。

否则，值不是 object 就直接返回之前的值 Obj\[Key\]。

这样就完成了任意层数的索引类型的添加 readonly 修饰：

![image](images/i7cKPZ64VbJqtqLFiErDrdg7DGhB-087WHPLnK9N-dE.webp)

我们取处理以后的索引 a 的值看一下，发现 b 已经加上了 readonly 修饰。

测试一下：

![image](images/uguWm8OPln_IJ5UhPA5AvZ1IV5tg9efMU--B_oSd9oU.webp)

[试一下](https://link.juejin.cn/?target=https%3A%2F%2Fwww.typescriptlang.org%2Fplay%3F%23code%2FC4TwDgpgBAIhFgEoQIYBMD2A7ANiAPAPIBGAVlBAB7ARZoDOUyAxhgE5r73BsCWWAcwA0UFFhAA%2BCVAC8UAN4AoKCqhtUmXCCgBtANIRt-KAGtDGAGZQSpALoAuZauc39h2xWq0GUDGQjMwE7OIVAA-NZkbiAeVDR0jABiAK5YgbzYwaHZEa4GMVBZ2SH2sPBIGth4RFH5thJF2aV57ooAvoqKoJC%2BZLIKWSilSsXEw42hzOPFxRalABQAlLLSAOSaAqtCEzNo0zMHEPsHJ1ACyWICpdx8gjunHafOjycvoW9QHR1d4NBwCMh0FUQMh6MkcMB%2Bv8KkCtPg-KQJDpVihVrYANydbp-cqAzR4UHg4AAJihuMqcIRSJRaORxFpq2YaMxQA "https://www.typescriptlang.org/play?#code/C4TwDgpgBAIhFgEoQIYBMD2A7ANiAPAPIBGAVlBAB7ARZoDOUyAxhgE5r73BsCWWAcwA0UFFhAA+CVAC8UAN4AoKCqhtUmXCCgBtANIRt-KAGtDGAGZQSpALoAuZauc39h2xWq0GUDGQjMwE7OIVAA-NZkbiAeVDR0jABiAK5YgbzYwaHZEa4GMVBZ2SH2sPBIGth4RFH5thJF2aV57ooAvoqKoJC+ZLIKWSilSsXEw42hzOPFxRalABQAlLLSAOSaAqtCEzNo0zMHEPsHJ1ACyWICpdx8gjunHafOjycvoW9QHR1d4NBwCMh0FUQMh6MkcMB+v8KkCtPg-KQJDpVihVrYANydbp-cqAzR4UHg4AAJihuMqcIRSJRaORxFpq2YaMxQA")

为啥这里没有计算呀？

因为 ts 的类型只有被用到的时候才会做计算。

所以可以在前面加上一段 Obj extends never ? never 或者 Obj extends any 等，从而触发计算：

```Plain Text
type DeepReadonly<Obj extends Record<string, any>> =
    Obj extends any
        ? {
            readonly [Key in keyof Obj]:
                Obj[Key] extends object
                    ? Obj[Key] extends Function
                        ? Obj[Key] 
                        : DeepReadonly<Obj[Key]>
                    : Obj[Key]
        }
        : never;

```
这样就显示了计算后的类型：

![image](images/mZ9Uqz3dcqCcXaxt3A7vsM6dsbDChShU1ZlgaQ4nkbo.webp)

而且写 Obj extends any 还有额外的好处就是能处理联合类型，这个可以看套路五，会有解释。

[试一下](https://link.juejin.cn/?target=https%3A%2F%2Fwww.typescriptlang.org%2Fplay%3F%23code%2FC4TwDgpgBAIhFgEoQIYBMD2A7ANiAPAPIBGAVlBAB7ARZoDOUyAxhgE5r73BsCWWAcwA0UFFhAA%2BCVAC8AKCiKoJclRp1GYkAqW6A-FADeO3aahtUmXCCgBtANIQb-KAGsnGAGbKyAXQBcJmbBKg5OvhTUtAxQGGQQzMBBwSlQBqGOIBFq0YwAYgCuWIm82MmpFelkYVlQ5RUp-rDwSJbYeETVmb4S9Q2KTRnhfQC%2BfU1YEABuEGwA3HJyoJCxZLJGQShNxinE2326zPv9up5NABQAlLLSAORWArdCB8FoxydmEO8fKQIFYgImtw%2BIIXhUxj8oBCGtCzLCoXIxktwNA4AhkOh2iBkPQCjhgOs0a1MdZ8HFSBI5kA "https://www.typescriptlang.org/play?#code/C4TwDgpgBAIhFgEoQIYBMD2A7ANiAPAPIBGAVlBAB7ARZoDOUyAxhgE5r73BsCWWAcwA0UFFhAA+CVAC8AKCiKoJclRp1GYkAqW6A-FADeO3aahtUmXCCgBtANIQb-KAGsnGAGbKyAXQBcJmbBKg5OvhTUtAxQGGQQzMBBwSlQBqGOIBFq0YwAYgCuWIm82MmpFelkYVlQ5RUp-rDwSJbYeETVmb4S9Q2KTRnhfQC+fU1YEABuEGwA3HJyoJCxZLJGQShNxinE2326zPv9up5NABQAlLLSAORWArdCB8FoxydmEO8fKQIFYgImtw+IIXhUxj8oBCGtCzLCoXIxktwNA4AhkOh2iBkPQCjhgOs0a1MdZ8HFSBI5kA")

## 总结
递归是把问题分解成一个个子问题，通过解决一个个子问题来解决整个问题。形式是不断的调用函数自身，直到满足结束条件。

在 TypeScript 类型系统中的高级类型也同样支持递归，**在类型体操中，遇到数量不确定的问题，要条件反射的想到递归。** 比如数组长度不确定、字符串长度不确定、索引类型层数不确定等。

如果说学完了提取和构造可以做一些基础的类型体操，那再加上递归就可以实现各种复杂类型体操了。

[本文案例的合并](https://link.juejin.cn/?target=https%3A%2F%2Fwww.typescriptlang.org%2Fplay%3F%23code%2FPTAEAUCcHsFsEsDOBTAXAKHSUARZyAHKOJZANQEMAbAV2QBUBPAtdAF2eV3yJgRUq0GnADzhQyAB5tkAOwAmiCH1IiasgNazoAd1kA%2BfaAC86UOYgTpcxcpIoR8WQDNkkUILpMWRsxf8A-B7UXpxWMgpKxPzIapraevp%2B-imgQXiE0aSewiwiOd7ISampqMFChckpZbLIAG5uANyYHCzcmSooAErIiDRUbCbtvPbkIbmxWQ5TsT0AxtCQ8iKIbJBOAOYANKAUsoyGhs1YYBkjMQWcAEzsYWczlyxXIvRGxqBV9OE2UZ2xTq53I8ih8Smlhg9xoVnsDiiUyvRjq0uPc-sCrj0lO9UaN0WI-vjRoSYiJZDRYAAjNyHfTHbCAB1NACN%2BgCx-zDYHoNSAoACCkEgGGRoA5bh5fJEvPcUgitnUWl0sgA2gBdN6giwS76RUAKgFuUAAMXgXLYOwAdObde5MWwlWrUkEFebTcKucgJSJrfodobjbaqv4yhKkWEXaLIJj%2BoN3qG3WKFQBGLZXLYAZi2ABYtgBWFV0sAASVkc1o8l6tzaheLNFLiHFfM1Mvi8uV3qc8nzMlgqqqGqlP21loNRtWZotLj11qV-osQXziAAogBHGjUEQ%2BkdDhQd5BdhtKNZ0af20AH5BH6qgSsl3oe3omzftztw0qgZzUFDBisL5er7k7ABCqoABTcnuoD-uCp6gGUb5UCgACUoAAGSgEBEF9lqoFBFBMHvsg8GflwV7Vr0EYDEMxE1iICY7MmoBpqAmagDmOzprSLRhJRpG9JGVwUUW161jRoB0QxTEsaAABs7EnEKO7QA026wOWXA9LACnIEpdaStYWqygkipKjsSk7GRgwYY2cp6MqQzKt2-i9rptg6uO7jrveTqDpO56zt%2BK5UGuw73kpRgWfukCHmCgRyepimdreG4mXJfQDM%2BUVlGpGladaxmdjsjrmmZra%2BmlAbJZGhExVlnZmUMmVxTu1GJsmyYpkZIkydg-40PAVDyBKFCMCp4E9X1A2MCIAAycgbGwAAWYFkpSbg7FU85UFw7z6fKq0OfWYWgNt1m2u8dkmD2fIKgA5BtsizXNV22gd013fNdopEEGrnmU3W9f1fKDVNM3zTs63IPlToSqDG25hxbS-WNfK1e8CP-ZAgNZp1YCAOragBk3oATHKAN4%2BgDR6myYA9AQVAUHMbpUFQw0U1TNPcnTIgAMprGBqzrHdu0WPqfBc2smx8%2BY9DQELPMbPZJQczp0pKAABgAJAA3oO03OGwAC%2BasC3Auvq65QrwBsc064r57RSrquazravi4bjPU7TAVdKb5venwOzi-o2uW1FFhlHLlXO8zdPI3JlMuyzAVXRsK53aACd7BsyeJxsV07PHGdZ6AV3yNAd1XVjoBy5s4sAKqyPARfDeXd1VzXRfs5zB3c5sMvmHLYE24O7mG15d7%2Bz5Q7GqAAA%2BZfC430DV7XsgJWwpVB6AtScsc9czxsTcL5HDc73PzeL1dc3IHT0Al3mcmcigcsM-UIrIHLrfy-2He8%2B9tXt9vQxXSXQwqg9wOn3Y2A81ZD1WP7d60UYwvxyqAG24DVZmX9r4MEGUeIDFDo-V0ctI5wLWCIU%2B58qCX1LoAIl9ACo%2BiTVkskzg9AoIXWQVAhrDQYcgJhRdWEiAAPIUgAFZgXmIsZYH9ti7H2IYc6-h%2BFCIOnsIaYIgiqythYSAnDmGsO1AAaWQIwUAThQAaH0dAZwoA5FKgwIHVIciFR6MYE9JyShoCCOQHMNgajA5BDsQ4pxCsDTqA8QvLxNjzA%2BMEfY-RfowlhLKBwrhLCJq%2BOiSvWJ0ELGRL8eebW30164Mqq4oR7xVH%2BAoGUUpJQKQVNCXMGp6TzDODKEBRCxgjAFyLpnLYoT-DyHqQ0-waBQCVIGakFOd0yjiJ6SUXJAzZk2PmakRZoBcm5PYTwRhWjGCRwSVskQRT2JAA "https://www.typescriptlang.org/play?#code/PTAEAUCcHsFsEsDOBTAXAKHSUARZyAHKOJZANQEMAbAV2QBUBPAtdAF2eV3yJgRUq0GnADzhQyAB5tkAOwAmiCH1IiasgNazoAd1kA+faAC86UOYgTpcxcpIoR8WQDNkkUILpMWRsxf8A-B7UXpxWMgpKxPzIapraevp+-imgQXiE0aSewiwiOd7ISampqMFChckpZbLIAG5uANyYHCzcmSooAErIiDRUbCbtvPbkIbmxWQ5TsT0AxtCQ8iKIbJBOAOYANKAUsoyGhs1YYBkjMQWcAEzsYWczlyxXIvRGxqBV9OE2UZ2xTq53I8ih8Smlhg9xoVnsDiiUyvRjq0uPc-sCrj0lO9UaN0WI-vjRoSYiJZDRYAAjNyHfTHbCAB1NACN+gCx-zDYHoNSAoACCkEgGGRoA5bh5fJEvPcUgitnUWl0sgA2gBdN6giwS76RUAKgFuUAAMXgXLYOwAdObde5MWwlWrUkEFebTcKucgJSJrfodobjbaqv4yhKkWEXaLIJj+oN3qG3WKFQBGLZXLYAZi2ABYtgBWFV0sAASVkc1o8l6tzaheLNFLiHFfM1Mvi8uV3qc8nzMlgqqqGqlP21loNRtWZotLj11qV-osQXziAAogBHGjUEQ+kdDhQd5BdhtKNZ0af20AH5BH6qgSsl3oe3omzftztw0qgZzUFDBisL5er7k7ABCqoABTcnuoD-uCp6gGUb5UCgACUoAAGSgEBEF9lqoFBFBMHvsg8GflwV7Vr0EYDEMxE1iICY7MmoBpqAmagDmOzprSLRhJRpG9JGVwUUW161jRoB0QxTEsaAABs7EnEKO7QA026wOWXA9LACnIEpdaStYWqygkipKjsSk7GRgwYY2cp6MqQzKt2-i9rptg6uO7jrveTqDpO56zt+K5UGuw73kpRgWfukCHmCgRyepimdreG4mXJfQDM+UVlGpGladaxmdjsjrmmZra+mlAbJZGhExVlnZmUMmVxTu1GJsmyYpkZIkydg-40PAVDyBKFCMCp4E9X1A2MCIAAycgbGwAAWYFkpSbg7FU85UFw7z6fKq0OfWYWgNt1m2u8dkmD2fIKgA5BtsizXNV22gd013fNdopEEGrnmU3W9f1fKDVNM3zTs63IPlToSqDG25hxbS-WNfK1e8CP-ZAgNZp1YCAOragBk3oATHKAN4+gDR6myYA9AQVAUHMbpUFQw0U1TNPcnTIgAMprGBqzrHdu0WPqfBc2smx8+Y9DQELPMbPZJQczp0pKAABgAJAA3oO03OGwAC+asC3Auvq65QrwBsc064r57RSrquazravi4bjPU7TAVdKb5venwOzi-o2uW1FFhlHLlXO8zdPI3JlMuyzAVXRsK53aACd7BsyeJxsV07PHGdZ6AV3yNAd1XVjoBy5s4sAKqyPARfDeXd1VzXRfs5zB3c5sMvmHLYE24O7mG15d7+z5Q7GqAAA+ZfC430DV7XsgJWwpVB6AtScsc9czxsTcL5HDc73PzeL1dc3IHT0Al3mcmcigcsM-UIrIHLrfy-2He8+9tXt9vQxXSXQwqg9wOn3Y2A81ZD1WP7d60UYwvxyqAG24DVZmX9r4MEGUeIDFDo-V0ctI5wLWCIU+58qCX1LoAIl9ACo+iTVkskzg9AoIXWQVAhrDQYcgJhRdWEiAAPIUgAFZgXmIsZYH9ti7H2IYc6-h+FCIOnsIaYIgiqythYSAnDmGsO1AAaWQIwUAThQAaH0dAZwoA5FKgwIHVIciFR6MYE9JyShoCCOQHMNgajA5BDsQ4pxCsDTqA8QvLxNjzA+MEfY-RfowlhLKBwrhLCJq+OiSvWJ0ELGRL8eebW30164Mqq4oR7xVH+AoGUUpJQKQVNCXMGp6TzDODKEBRCxgjAFyLpnLYoT-DyHqQ0-waBQCVIGakFOd0yjiJ6SUXJAzZk2PmakRZoBcm5PYTwRhWjGCRwSVskQRT2JAA")

(其实这节的 IsEqual 判断是不完善的，套路六里面会讲原因)
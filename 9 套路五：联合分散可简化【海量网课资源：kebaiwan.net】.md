# 9 套路五：联合分散可简化
联合类型在类型编程中是比较特殊的，TypeScript 对它做了专门的处理，写法上可以简化，但也增加了一些认知成本。

这是类型体操的第五个套路：联合分散可简化。

## 分布式条件类型
**当类型参数为联合类型，并且在条件类型左边直接引用该类型参数的时候，TypeScript 会把每一个元素单独传入来做类型运算，最后再合并成联合类型，这种语法叫做分布式条件类型。**

比如这样一个联合类型：

```Plain Text
type Union = 'a' | 'b' | 'c';

```
我们想把其中的 a 大写，就可以这样写：

```Plain Text
type UppercaseA<Item extends string> = 
    Item extends 'a' ?  Uppercase<Item> : Item;

```
![image](images/SGeUrdUFaCsGoyj3ALKYQbBmEbvQuNO0nKfOTR13NKM.webp)

可以看到，我们类型参数 Item 约束为 string，条件类型的判断中也是判断是否是 a，但传入的是联合类型。

这就是 TypeScript 对联合类型在条件类型中使用时的特殊处理：会把联合类型的每一个元素单独传入做类型计算，最后合并。

这和联合类型遇到字符串时的处理一样：

![image](images/6jXqKB5dkJj6WiscBwYZO1KKbU9cgwAHBG9LfR78QKg.webp)

这样确实是简化了类型编程逻辑的，不需要递归提取每个元素再处理。

TypeScript 之所以这样处理联合类型也很容易理解，因为联合类型的每个元素都是互不相关的，不像数组、索引、字符串那样元素之间是有关系的。所以设计成了每一个单独处理，最后合并。

知道了 TypeScript 怎么处理的联合类型，趁热打铁来练习一下：

## CamelcaseUnion
Camelcase 我们实现过，就是提取字符串中的字符，首字母大写以后重新构造一个新的。

```Plain Text
type Camelcase<Str extends string> = 
    Str extends `${infer Left}_${infer Right}${infer Rest}`
    ? `${Left}${Uppercase<Right>}${Camelcase<Rest>}`
    : Str;

```
提取 \_ 左右的字符，把右边字符大写之后构造成新的字符串，余下的字符串递归处理。

![image](images/lEbB9IDlHPEiBkMjqRUtHURjqBRYjiMwjotkV7EqM8Y.webp)

如果是对字符串数组做 Camelcase，那就要递归处理每一个元素：

```Plain Text
type CamelcaseArr<
  Arr extends unknown[]
> = Arr extends [infer Item, ...infer RestArr]
  ? [Camelcase<Item & string>, ...CamelcaseArr<RestArr>]
  : [];

```
类型参数 Arr 为待处理数组。

递归提取每一个元素做 Camelcase，因为 Camelcase 要求传入 string，这里要 & string 来变成 string 类型。

![image](images/dRbAmOJS9qawsfu-l-WMEl-dq0cio2SO9avdmoy7D7Y.webp)

那如果是联合类型呢？

联合类型不需要递归提取每个元素，TypeScript 内部会把每一个元素传入单独做计算，之后把每个元素的计算结果合并成联合类型。

```Plain Text
type CamelcaseUnion<Item extends string> = 
  Item extends `${infer Left}_${infer Right}${infer Rest}` 
    ? `${Left}${Uppercase<Right>}${CamelcaseUnion<Rest>}` 
    : Item;

```
![image](images/TephNAgDMa7frRAoBWD_agwpDPW6rJAE7Bqw_fABgKY.webp)

[试一下](https://link.juejin.cn/?target=https%3A%2F%2Fwww.typescriptlang.org%2Fplay%3F%23code%2FFAFwngDgpgBAwgQwLZQDYGMEGcoB4DKIATjFAB4hQB2AJljFsQJZUDmAfDALwzAz8xCJcpVr0ABgBIA3iwBmUEgBkockAF8A%2BjPmKYAJSasAFhp1UFJfVEbrxfAQH4YU6SrXqZAVQjQimHFxDExB2T2lEFAxsPGtGMPsBGAAuQWIAbmBQSFhItACoOIBXVBBueGR8mNwAcgQETXrGhBr2TOzoCqiCgEEiIlwHPuEKajoYIqoAayoAewB3KgBtAF1gTh5h0lGxGCXdEgBJSiQAGhgAOiuDgxsQYbX%2BZyW86MDjqCQYADIGZjZ2OcrhdXr1%2BkE7sN2I8UnsVu1wJ1QTFhsVSuVkThhrglnUGk16jVzjUAEYkzRkikkokwGrodCaemM9A1FZtLKI3KVN5QLxUJizKi4D5fERjeiMIgsDjlBwi7aicauG7uDTaWQWPTBUzhG5xDTiXhJZyuVXhHx%2BApBIymMIyTG8-mCiHxOxGgSpEUInJdKo4PkCqhoso8B0B5145rNGowAA%2BtMpiepcdpTLTLLaQA "https://www.typescriptlang.org/play?#code/FAFwngDgpgBAwgQwLZQDYGMEGcoB4DKIATjFAB4hQB2AJljFsQJZUDmAfDALwzAz8xCJcpVr0ABgBIA3iwBmUEgBkockAF8A+jPmKYAJSasAFhp1UFJfVEbrxfAQH4YU6SrXqZAVQjQimHFxDExB2T2lEFAxsPGtGMPsBGAAuQWIAbmBQSFhItACoOIBXVBBueGR8mNwAcgQETXrGhBr2TOzoCqiCgEEiIlwHPuEKajoYIqoAayoAewB3KgBtAF1gTh5h0lGxGCXdEgBJSiQAGhgAOiuDgxsQYbX+ZyW86MDjqCQYADIGZjZ2OcrhdXr1+kE7sN2I8UnsVu1wJ1QTFhsVSuVkThhrglnUGk16jVzjUAEYkzRkikkokwGrodCaemM9A1FZtLKI3KVN5QLxUJizKi4D5fERjeiMIgsDjlBwi7aicauG7uDTaWQWPTBUzhG5xDTiXhJZyuVXhHx+ApBIymMIyTG8-mCiHxOxGgSpEUInJdKo4PkCqhoso8B0B5145rNGowAA+tMpiepcdpTLTLLaQA")

这不和单个字符串的处理没区别么？

没错，对联合类型的处理和对单个类型的处理没什么区别，TypeScript 会把每个单独的类型拆开传入。不需要像数组类型那样需要递归提取每个元素做处理。

确实简化了很多，好像都是优点？

也不全是，其实这样处理也增加了一些认知成本，不信我们再来看个例子：

## IsUnion
判断联合类型我们会这样写：

```Plain Text
type IsUnion<A, B = A> =
    A extends A
        ? [B] extends [A]
            ? false
            : true
        : never

```
当传入联合类型时，会返回 true：

![image](images/qCTPH8FTZlynSj4TGa5CF4JHHYesCHha7oH1Cq9rUrA.webp)

当传入其他类型时，会返回 false： 

![image](images/6NqL9nek77fmxFDqhbtGO71AAvw6gMsJYZhe2XXZ2t0.webp)

[试一下](https://link.juejin.cn/?target=https%3A%2F%2Fwww.typescriptlang.org%2Fplay%3F%23code%2FC4TwDgpgBAkgzgVQHYEsD2SA8BBANFAISgF4psA%2BEgKClrKggA9gIkATOMmungfigDaBALoNmrDoOzDuPOVH4AzAIYAbOBFny6ALijAATgFdN2qHqQQAbhANUqoSLESoMAJQhwjq4CWfJ0LAByZSCAHyCAI3CggGMYtiDyAG57R2h4APdPb2AAJj9M1ywBKBCgqDCy6Mqy%2BOEUoA "https://www.typescriptlang.org/play?#code/C4TwDgpgBAkgzgVQHYEsD2SA8BBANFAISgF4psA+EgKClrKggA9gIkATOMmungfigDaBALoNmrDoOzDuPOVH4AzAIYAbOBFny6ALijAATgFdN2qHqQQAbhANUqoSLESoMAJQhwjq4CWfJ0LAByZSCAHyCAI3CggGMYtiDyAG57R2h4APdPb2AAJj9M1ywBKBCgqDCy6Mqy+OEUoA")

是不是在心里会问：什么鬼？这段逻辑是啥？

这就是分布式条件类型带来的认知成本。

我们先来看这样一个类型：

```Plain Text
type TestUnion<A, B = A> = A  extends A ? { a: A, b: B} : never;

type TestUnionResult = TestUnion<'a' | 'b' | 'c'>;

```
传入联合类型 'a' | 'b' | 'c' 的时候，结果是这样的：

![image](images/cdRk9u-t9o-pdTsCDLexYqIaBn-cLzVYZNJjk2ckd9A.webp)

A 和 B 都是同一个联合类型，为啥值还不一样呢？

因为条件类型中如果左边的类型是联合类型，会把每个元素单独传入做计算，而右边不会。

所以 A 是 'a' 的时候，B 是 'a' | 'b' | 'c'， A 是 'b' 的时候，B 是 'a' | 'b' | 'c'。。。

[试一下](https://link.juejin.cn/?target=https%3A%2F%2Fwww.typescriptlang.org%2Fplay%3F%23code%2FC4TwDgpgBAKhDOwCqA7AlgexQHgIIBooAhKAXilwD4yKooIAPYCFAE3loH4oBvKAQwBcFQgCNhRAL5RhKCADcIAJwDcAKDWhIsBMnRYASggCuAG2A04iVJhwByfnagAfKHdFPXdgMZ3KKoA "https://www.typescriptlang.org/play?#code/C4TwDgpgBAKhDOwCqA7AlgexQHgIIBooAhKAXilwD4yKooIAPYCFAE3loH4oBvKAQwBcFQgCNhRAL5RhKCADcIAJwDcAKDWhIsBMnRYASggCuAG2A04iVJhwByfnagAfKHdFPXdgMZ3KKoA")

那么利用这个特点就可以实现 Union 类型的判断：

```Plain Text
type IsUnion<A, B = A> =
    A extends A
        ? [B] extends [A]
            ? false
            : true
        : never

```
类型参数 A、B 是待判断的联合类型，B 默认值为 A，也就是同一个类型。

A extends A 这段看似没啥意义，主要是为了触发分布式条件类型，让 A 的每个类型单独传入。

\[B\] extends \[A\] 这样不直接写 B 就可以避免触发分布式条件类型，那么 B 就是整个联合类型。

B 是联合类型整体，而 A 是单个类型，自然不成立，而其它类型没有这种特殊处理，A 和 B 都是同一个，怎么判断都成立。

利用这个特点就可以判断出是否是联合类型。

其中有两个点比较困惑，我们重点记一下：

**当 A 是联合类型时：**

* **A extends A 这种写法是为了触发分布式条件类型，让每个类型单独传入处理的，没别的意义。**
* **A extends A 和 \[A\] extends \[A\] 是不同的处理，前者是单个类型和整个类型做判断，后者两边都是整个联合类型，因为只有 extends 左边直接是类型参数才会触发分布式条件类型。**

理解了这两点，分布式条件类型就算掌握了。

掌握了难点之后，我们再做些练习：

## BEM
bem 是 css 命名规范，用 block\_\_element--modifier 的形式来描述某个区块下面的某个元素的某个状态的样式。

那么我们可以写这样一个高级类型，传入 block、element、modifier，返回构造出的 class 名：

这样使用：

```Plain Text
type bemResult = BEM<'guang', ['aaa', 'bbb'], ['warning', 'success']>;

```
它的实现就是三部分的合并，但传入的是数组，要递归遍历取出每一个元素来和其他部分组合，这样太麻烦了。

而如果是联合类型就不用递归遍历了，因为联合类型遇到字符串也是会单独每个元素单独传入做处理。

数组转联合类型可以这样写：

![image](images/Oe46MkVCGZC-z_ZYwhRB-c4oeQXu7fGwf3og-Iiz3bw.webp)

那么 BEM 就可以这样实现：

```Plain Text
type BEM<
    Block extends string,
    Element extends string[],
    Modifiers extends string[]
> = `${Block}__${Element[number]}--${Modifiers[number]}`;

```
类型参数 Block、Element、Modifiers 分别是 bem 规范的三部分，其中 Element 和 Modifiers 都可能多个，约束为 string\[\]。

构造一个字符串类型，其中 Element 和 Modifiers 通过索引访问来变为联合类型。

字符串类型中遇到联合类型的时候，会每个元素单独传入计算，也就是这样的效果：



![image](images/fqEiPidLD8JgurxdXyaaQVmNudJNCxsf5Yu4sWoA7ZM.webp)

 [试一下](https://link.juejin.cn/?target=https%3A%2F%2Fwww.typescriptlang.org%2Fplay%3F%23code%2FC4TwDgpgBAQgogWQDwCgrtgGwPYGMDWUEAHsBAHYAmAzlNcAE4CW5A5gDRoZyYQC2FYEVIUadRi1YBtALqcMUBNkpMAZkwgNaJMlVr1mbWSgB8UALxQABgBIA3jBwEAvgH1X9nv0FTyAVz4AI00ZZwBaMPslFXVNal8A4IZQqwBuFAzQSChgvgAlCGo-TCFLeGQAclY-AEM2CvYoKQqa1oaoCsCuirkmioB3GoZySXaKotxcQuoek1SgA "https://www.typescriptlang.org/play?#code/C4TwDgpgBAQgogWQDwCgrtgGwPYGMDWUEAHsBAHYAmAzlNcAE4CW5A5gDRoZyYQC2FYEVIUadRi1YBtALqcMUBNkpMAZkwgNaJMlVr1mbWSgB8UALxQABgBIA3jBwEAvgH1X9nv0FTyAVz4AI00ZZwBaMPslFXVNal8A4IZQqwBuFAzQSChgvgAlCGo-TCFLeGQAclY-AEM2CvYoKQqa1oaoCsCuirkmioB3GoZySXaKotxcQuoek1SgA")

可以看到，用好了联合类型，确实能简化类型编程逻辑。

## AllCombinations
我们再来实现一个全组合的高级类型，也是联合类型相关的：

希望传入 'A' | 'B' 的时候，能够返回所有的组合： 'A' | 'B' | 'BA' | 'AB'。

这种全组合问题的实现思路就是两两组合，组合出的字符串再和其他字符串两两组和：

比如 'A' | 'B' | 'c'，就是 A 和 B、C 组合，B 和 A、C 组合，C 和 A、B 组合。然后组合出来的字符串再和其他字符串组合。

任何两个类型的组合有四种：A、B、AB、BA

```Plain Text
type Combination<A extends string, B extends string> =
    | A
    | B
    | `${A}${B}`
    | `${B}${A}`;

```
然后构造出来的字符串再和其他字符串组合。

所以全组合的高级类型就是这样：

```Plain Text
type AllCombinations<A extends string, B extends string = A> = 
    A extends A
        ? Combination<A, AllCombinations<Exclude<B, A>>>
        : never;

```
类型参数 A、B 是待组合的两个联合类型，B 默认是 A 也就是同一个。

A extends A 的意义就是让联合类型每个类型单独传入做处理，上面我们刚学会。

A 的处理就是 A 和 B 中去掉 A 以后的所有类型组合，也就是 Combination<A, B 去掉 A 以后的所有组合>。

而 B 去掉 A 以后的所有组合就是 AllCombinations<Exclude<B, A>>，所以全组合就是 Combination<A, AllCombinations<Exclude<B, A>>>。

![image](images/V7bm9VBTIB2a_9c_c4i_EXfobTUD5iOWCTgrS_Rsvss.webp)

这里利用到了分布式条件类型的特性，通过 A extends A 来取出联合类型中的单个类型。

[试一下](https://link.juejin.cn/?target=https%3A%2F%2Fwww.typescriptlang.org%2Fplay%3F%23code%2FC4TwDgpgBAwg9gWwEYEsB2BDYK5oDwCCUEAHsBGgCYDOU1wATugOYA0UAQsWRTXYywB8UALwAoKJKgAfKAQlTZHBZNkADACQBvAgF9tHXWpUyomrYe161AbgljQkOQBtn8ZOiw401Qt3JUtPRMaGyc-rxBAqGicsIichGBciYA-LCIqJjYuITsBK7uWV64vgCiJADGzgCulBB4HPmCLSYAXFBoEABuEAx2DuDQBW6Znjk%2BAEoQ1DXOwLEjRePevgDkBGumaxxbsmswa4I2QA "https://www.typescriptlang.org/play?#code/C4TwDgpgBAwg9gWwEYEsB2BDYK5oDwCCUEAHsBGgCYDOU1wATugOYA0UAQsWRTXYywB8UALwAoKJKgAfKAQlTZHBZNkADACQBvAgF9tHXWpUyomrYe161AbgljQkOQBtn8ZOiw401Qt3JUtPRMaGyc-rxBAqGicsIichGBciYA-LCIqJjYuITsBK7uWV64vgCiJADGzgCulBB4HPmCLSYAXFBoEABuEAx2DuDQBW6Znjk+AEoQ1DXOwLEjRePevgDkBGumaxxbsmswa4I2QA")

## 总结
**联合类型中的每个类型都是相互独立的，TypeScript 对它做了特殊处理，也就是遇到字符串类型、条件类型的时候会把每个类型单独传入做计算，最后把每个类型的计算结果合并成联合类型。**

条件类型左边是联合类型的时候就会触法这种处理，叫做分布式条件类型。

有两点特别要注意：

* A extends A 不是没意义，意义是取出联合类型中的单个类型放入 A
* A extends A 才是分布式条件类型， \[A\] extends \[A\] 就不是了，只有左边是单独的类型参数才可以。

我们后面做了一些案例，发现联合类型的这种 distributive 的特性确实能简化类型编程，但是也增加了认知成本，不过这也是不可避免的事。

[本文案例的合并](https://link.juejin.cn/?target=https%3A%2F%2Fwww.typescriptlang.org%2Fplay%3F%23code%2FPTAEGEEMFsFMBsDGkDOsBQAXAngB1hDAsmgDwDKmATqLAB6awB2AJiqCtQJZMDmAfKAC8odKHGhKNeo1bsABgBIA3jwBmsGgBlYazAF8A%2BivWbQAJS68AFgZNMNNc7E775YiQH5QS5Tr36KgCquPhUJLCkljaY-IHKUHBIqJHOnHHuEqAAXJLUANzoWHgEicQpaQCu8JjChEkRpADkkJCGre2QTfyFxfj15WgAglRUpB4j0gzMbKCVTADWTAD2AO5MANoAuuiCIpO003KgG6Y0AJKM0AA0oAB0D2cWLpiTO%2BLeG2XJZJew0KAAGQcbh8fi3B53b4RSZRF6TfjvHInLa9HD9aEpSZVGp1THDUakDYtNodVpNW5NABGVMMNLpVIpoCaiEQhlZ7MQTS2PSK6NKRB%2BsCCTC4yyYpD%2BAJkM3YnCoPAEdQ8UsOslmvie-gMxlUDjM0Vs8SeaQM8lEWW8vm18RCYUahti8XxwtF4rh6TcFokuSlaJKAyFIrFTBxtRELuD7pJnU6TVAAB9mfSU4zE8yOZmubz0CBQOcUFGmHyAwWi6QhrcAEJ1IZ7DziIZq2WgIYNrKgT5VrbN44bIZIjsd7xqSDwNDtofiXLUSoYKfIpiwABumhL-TLbtDLmq4fzha3zS6CepTRPXJPLG6-o3B5DYYATHVNyGicyuunT5%2BuTzenmACovEWfQEIBnDlpWoA1vseytuIMrHE23jKKAkC5JBVK5FW%2BiLiumg3qBQFbmGdRgZg5YtPGSZftR2Z-mAVYAKIALIgVBLHjFkVbwMsiALL2szyoq1ztox8D-MwtQIYJoK8NsIlZMxywsFwahcJo7DSXKsnbLsdS%2BNxvELEYupiRJTCYBsTCVNAVKaFs%2BgALSOSoSkqWpGlWTZdlUA58i9GxdnQCRIhMcxzS8JUkB8EyxKtF0lI0oyWy3MSqyQFQooxZSKCVKyLgoNyOZ5kM8DwOAyy2TwkCYCGKBsRVVVMDVr5NlpIIKnw1YCdpnVKkI7ZJm2WRJlWg0%2BCoQzxNhmQSEmBnxFN-liGxpXlZVVLVbV4ooBWPUdcJUH7UJfC1rBbVHLMw1eBAG1ba1txrY1m3NdtTC7YxdCIPAlQsJEVaPfwQPtrkS6rlQBGtmVz33TtIVQ%2BtTUtTtzRDFRzJVujTTgNeQA "https://www.typescriptlang.org/play?#code/PTAEGEEMFsFMBsDGkDOsBQAXAngB1hDAsmgDwDKmATqLAB6awB2AJiqCtQJZMDmAfKAC8odKHGhKNeo1bsABgBIA3jwBmsGgBlYazAF8A+ivWbQAJS68AFgZNMNNc7E775YiQH5QS5Tr36KgCquPhUJLCkljaY-IHKUHBIqJHOnHHuEqAAXJLUANzoWHgEicQpaQCu8JjChEkRpADkkJCGre2QTfyFxfj15WgAglRUpB4j0gzMbKCVTADWTAD2AO5MANoAuuiCIpO003KgG6Y0AJKM0AA0oAB0D2cWLpiTO+LeG2XJZJew0KAAGQcbh8fi3B53b4RSZRF6TfjvHInLa9HD9aEpSZVGp1THDUakDYtNodVpNW5NABGVMMNLpVIpoCaiEQhlZ7MQTS2PSK6NKRB+sCCTC4yyYpD+AJkM3YnCoPAEdQ8UsOslmvie-gMxlUDjM0Vs8SeaQM8lEWW8vm18RCYUahti8XxwtF4rh6TcFokuSlaJKAyFIrFTBxtRELuD7pJnU6TVAAB9mfSU4zE8yOZmubz0CBQOcUFGmHyAwWi6QhrcAEJ1IZ7DziIZq2WgIYNrKgT5VrbN44bIZIjsd7xqSDwNDtofiXLUSoYKfIpiwABumhL-TLbtDLmq4fzha3zS6CepTRPXJPLG6-o3B5DYYATHVNyGicyuunT5+uTzenmACovEWfQEIBnDlpWoA1vseytuIMrHE23jKKAkC5JBVK5FW+iLiumg3qBQFbmGdRgZg5YtPGSZftR2Z-mAVYAKIALIgVBLHjFkVbwMsiALL2szyoq1ztox8D-MwtQIYJoK8NsIlZMxywsFwahcJo7DSXKsnbLsdS+NxvELEYupiRJTCYBsTCVNAVKaFs+gALSOSoSkqWpGlWTZdlUA58i9GxdnQCRIhMcxzS8JUkB8EyxKtF0lI0oyWy3MSqyQFQooxZSKCVKyLgoNyOZ5kM8DwOAyy2TwkCYCGKBsRVVVMDVr5NlpIIKnw1YCdpnVKkI7ZJm2WRJlWg0+CoQzxNhmQSEmBnxFN-liGxpXlZVVLVbV4ooBWPUdcJUH7UJfC1rBbVHLMw1eBAG1ba1txrY1m3NdtTC7YxdCIPAlQsJEVaPfwQPtrkS6rlQBGtmVz33TtIVQ+tTUtTtzRDFRzJVujTTgNeQA")